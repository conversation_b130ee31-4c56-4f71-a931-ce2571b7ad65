#include "ChessUI.h"
#include <iostream>
#include <algorithm>
#include <cctype>

ChessUI::ChessUI() : engine_(Color::BLACK, 4), humanIsWhite_(true) {}

void ChessUI::run()
{
    displayWelcome();
    setupGame();

    while (game_.getGameState() == GameState::PLAYING ||
           game_.getGameState() == GameState::CHECK)
    {

        displayBoard();
        displayGameStatus();

        if ((game_.getCurrentPlayer() == Color::WHITE && humanIsWhite_) ||
            (game_.getCurrentPlayer() == Color::BLACK && !humanIsWhite_))
        {
            playHumanTurn();
        }
        else
        {
            playComputerTurn();
        }
    }

    displayBoard();
    displayGameOver();
}

void ChessUI::setupGame()
{
    humanIsWhite_ = askHumanColor();
    engine_.setColor(humanIsWhite_ ? Color::BLACK : Color::WHITE);
    game_.startNewGame();

    std::cout << "\nGame setup complete!\n";
    std::cout << "You are playing as " << (humanIsWhite_ ? "White" : "Black") << "\n";
    std::cout << "Enter moves in algebraic notation (e.g., 'e2e4')\n";
    std::cout << "Type 'help' for more commands.\n\n";
}

bool ChessUI::askHumanColor()
{
    std::string input;
    while (true)
    {
        std::cout << "Do you want to play as White or Black? (w/b): ";
        std::getline(std::cin, input);
        input = toLowerCase(input);

        if (input == "w" || input == "white")
        {
            return true;
        }
        else if (input == "b" || input == "black")
        {
            return false;
        }
        else
        {
            std::cout << "Please enter 'w' for White or 'b' for Black.\n";
        }
    }
}

void ChessUI::playHumanTurn()
{
    while (true)
    {
        std::string input = getPlayerInput();

        if (processCommand(input))
        {
            continue; // Command was processed, get next input
        }

        Move move = parseMove(input);
        if (move.isValid())
        {
            bool success = game_.makeMove(move);
            displayMoveResult(success, input);
            if (success)
            {
                break; // Valid move made, end turn
            }
        }
        else
        {
            std::cout << "Invalid move format. Use algebraic notation (e.g., 'e2e4')\n";
        }
    }
}

void ChessUI::playComputerTurn()
{
    std::cout << "Computer is thinking...\n";

    // Use time-controlled search (5 seconds)
    Move computerMove = engine_.getBestMoveWithTime(game_, 5000);

    if (computerMove.isValid())
    {
        bool success = game_.makeMove(computerMove);
        if (success)
        {
            std::cout << "Computer plays: " << computerMove.toAlgebraic() << "\n";

            // Show analysis information
            std::cout << engine_.getAnalysisString() << "\n";
        }
        else
        {
            std::cout << "Error: Computer made invalid move!\n";
        }
    }
    else
    {
        std::cout << "Computer has no valid moves!\n";
    }
}

std::string ChessUI::getPlayerInput()
{
    std::string input;
    std::cout << colorToString(game_.getCurrentPlayer()) << " to move: ";
    std::getline(std::cin, input);
    return input;
}

Move ChessUI::parseMove(const std::string &input)
{
    std::string cleanInput = toLowerCase(input);

    // Remove spaces
    cleanInput.erase(std::remove(cleanInput.begin(), cleanInput.end(), ' '), cleanInput.end());

    return Move::fromAlgebraic(cleanInput);
}

bool ChessUI::processCommand(const std::string &input)
{
    std::string command = toLowerCase(input);

    if (command == "help" || command == "h")
    {
        displayHelp();
        return true;
    }
    else if (command == "board" || command == "b")
    {
        displayBoard();
        return true;
    }
    else if (command == "status" || command == "s")
    {
        displayGameStatus();
        return true;
    }
    else if (command == "analyze" || command == "a")
    {
        analyzePosition();
        return true;
    }
    else if (command == "hint")
    {
        showHint();
        return true;
    }
    else if (command == "quit" || command == "q" || command == "exit")
    {
        std::cout << "Thanks for playing!\n";
        exit(0);
    }

    return false; // Not a command
}

void ChessUI::displayWelcome()
{
    clearScreen();
    std::cout << "========================================\n";
    std::cout << "         WELCOME TO CHESS ENGINE       \n";
    std::cout << "========================================\n\n";
    std::cout << "A complete chess engine with AI opponent\n";
    std::cout << "Supports all chess rules including castling,\n";
    std::cout << "en passant, and pawn promotion.\n\n";
}

void ChessUI::displayHelp()
{
    std::cout << "\n========== HELP ==========\n";
    std::cout << "Move format: Use algebraic notation\n";
    std::cout << "  Examples: e2e4, g1f3, e7e8q (pawn promotion)\n\n";
    std::cout << "Commands:\n";
    std::cout << "  help, h     - Show this help\n";
    std::cout << "  board, b    - Display current board\n";
    std::cout << "  status, s   - Show game status\n";
    std::cout << "  analyze, a  - Analyze current position\n";
    std::cout << "  hint        - Get move suggestion\n";
    std::cout << "  quit, q     - Exit the game\n\n";
    std::cout << "Special moves:\n";
    std::cout << "  Castling: Move king two squares (e1g1, e1c1)\n";
    std::cout << "  En passant: Capture pawn that just moved two squares\n";
    std::cout << "  Promotion: Add piece letter at end (e7e8q for queen)\n";
    std::cout << "    q=Queen, r=Rook, b=Bishop, n=Knight\n";
    std::cout << "==========================\n\n";
}

void ChessUI::displayBoard()
{
    std::cout << game_.getBoardString() << std::endl;
}

void ChessUI::displayGameStatus()
{
    std::cout << game_.getGameStatusString() << "\n";

    if (!game_.getMoveHistory().empty())
    {
        std::cout << "Last move: " << game_.getLastMoveString() << "\n";
    }

    std::cout << std::endl;
}

void ChessUI::displayMoveResult(bool success, const std::string &move)
{
    if (success)
    {
        std::cout << "Move " << move << " played successfully!\n\n";
    }
    else
    {
        std::cout << "Invalid move: " << move << "\n";
        std::cout << "Please try again.\n\n";
    }
}

void ChessUI::displayGameOver()
{
    std::cout << "\n========== GAME OVER ==========\n";
    std::cout << game_.getGameStatusString() << "\n";

    switch (game_.getGameState())
    {
    case GameState::CHECKMATE:
        if ((game_.getCurrentPlayer() == Color::WHITE && !humanIsWhite_) ||
            (game_.getCurrentPlayer() == Color::BLACK && humanIsWhite_))
        {
            std::cout << "Congratulations! You won!\n";
        }
        else
        {
            std::cout << "Computer wins! Better luck next time.\n";
        }
        break;
    case GameState::STALEMATE:
    case GameState::DRAW:
        std::cout << "The game ended in a draw.\n";
        break;
    default:
        break;
    }

    std::cout << "===============================\n\n";
    std::cout << "Thanks for playing!\n";
}

void ChessUI::clearScreen()
{
// Cross-platform screen clearing
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void ChessUI::waitForEnter()
{
    std::cout << "Press Enter to continue...";
    std::cin.ignore();
}

std::string ChessUI::toLowerCase(const std::string &str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

void ChessUI::analyzePosition()
{
    std::cout << "\n=== POSITION ANALYSIS ===\n";

    // Get current position evaluation
    Color currentPlayer = game_.getCurrentPlayer();
    int evaluation = engine_.evaluatePosition(game_.getBoard(), currentPlayer);

    std::cout << "Position evaluation for " << colorToString(currentPlayer) << ": ";
    if (evaluation > 0)
    {
        std::cout << "+" << evaluation << " (advantage)\n";
    }
    else if (evaluation < 0)
    {
        std::cout << evaluation << " (disadvantage)\n";
    }
    else
    {
        std::cout << "0 (equal)\n";
    }

    // Show game state
    std::cout << "Game state: ";
    switch (game_.getGameState())
    {
    case GameState::PLAYING:
        std::cout << "In progress\n";
        break;
    case GameState::CHECK:
        std::cout << "Check!\n";
        break;
    case GameState::CHECKMATE:
        std::cout << "Checkmate!\n";
        break;
    case GameState::STALEMATE:
        std::cout << "Stalemate\n";
        break;
    case GameState::DRAW:
        std::cout << "Draw\n";
        break;
    }

    // Show number of legal moves
    std::vector<Move> validMoves = game_.getAllValidMoves();
    std::cout << "Legal moves available: " << validMoves.size() << "\n";

    std::cout << "\n";
}

void ChessUI::showHint()
{
    std::cout << "\nAnalyzing position for best move...\n";

    // Get best move from engine
    Move bestMove = engine_.getBestMoveWithTime(game_, 3000); // 3 second analysis

    if (bestMove.isValid())
    {
        std::cout << "Suggested move: " << bestMove.toAlgebraic() << "\n";

        // Show analysis
        std::cout << engine_.getAnalysisString();
    }
    else
    {
        std::cout << "No valid moves available!\n";
    }

    std::cout << "\n";
}
